//+------------------------------------------------------------------+
//|                                        RSI Swing SAR No Stop V12 |
//|                                    RSI and SAR based swing trader |
//+------------------------------------------------------------------+
#property version     "12"
#property description "RSI Swing trading strategy with SAR trailing stop and S/R-based take profit, risk percentage management and fixed lot size option"

#define RsiStochSwing 9765426

#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

// Strategy parameters
input bool   InpTimeoutEnable       = true;           // Timeout enable
input int    InpTimeout             = 0;              // Timeout threshold (seconds)
input double InpTrailingStopFactor  = 1;              // Trailing stop factor
input int    InpFastRSIPeriod       = 7;              // Fast RSI period
input int    InpDailyRSIPeriod      = 30;             // Daily RSI period
input double InpHardStopOffset      = 50;             // Hard stop offset
input bool   InpUseSrTrailing       = false;          // Use support/resistance based take profit
input bool   InpUseProfitScoreTrailing = false;       // Use profit score based trailing
input double InpProfitScoreThr      = 0.5;            // Profit score threshold for trailing activation
input double InpProfitNormMax       = 100000;         // Profit normalization max value
input double InpOpenTimeNormMax     = 100000;         // Open time normalization max value (seconds)

input double InpDailyRsiUpperLimit  = 55;             // Daily RSI upper limit
input bool   InpTimeFilterEnable    = false;          // Time filter enable
input double InpSARStepSize         = 0.03;           // SAR step size
input double InpRRfactor            = 20;             // Risk/reward ratio factor
input bool   InpUseFixedLotSize     = false;          // Use fixed lot size instead of risk percentage
input double InpFixedLotSize        = 0.01;           // Fixed lot size (when InpUseFixedLotSize = true)
input double InpRiskPercent         = 2.0;            // Risk percentage per trade (when InpUseFixedLotSize = false)
input ENUM_TIMEFRAMES InpTimeFrame  = PERIOD_M1;      // Timeframe
input ENUM_TIMEFRAMES InpSRTimeFrame = PERIOD_H1;     // Support/Resistance timeframe
input int    InpSRLookbackPeriod    = 20;             // Support/Resistance lookback period
input double InpSRLowThr            = 0.8;            // S/R ratio lower threshold
input double InpSRHighThr           = 1.2;            // S/R ratio upper threshold
input int    InpSRBuffer            = 1000;           // S/R buffer in points
input double InpSrPriceChange       = 1.0;            // S/R price change threshold (%)

// Global variables
int start_hour = 12;
int start_min = 0;
int end_hour = 18;
int end_min = 0;

//+------------------------------------------------------------------+
//| RSI Swing SAR Expert Advisor class                              |
//+------------------------------------------------------------------+
class CSampleExpert
  {
protected:
   // Trading objects
   double            m_adjusted_point;             // Point value adjusted for 3 or 5 digits
   CSymbolInfo       m_symbol;                     // Symbol info object
   CTrade            m_trade;                      // Trading object
   CPositionInfo     m_position;                   // Position info object
   CAccountInfo      m_account;                    // Account info wrapper

   // Indicator handles
   int               m_handle_fastRsi;             // Fast RSI handle
   int               m_handle_dailyRsi;            // Daily RSI handle
   int               m_handle_sar;                 // SAR indicator handle

   // Indicator buffers
   double            m_buff_fastRSI[];             // Fast RSI buffer
   double            m_buff_dailyRSI[];            // Daily RSI buffer
   double            m_buff_SAR[];                 // SAR buffer
   double            m_buff_high[];                // High price buffer for S/R
   double            m_buff_low[];                 // Low price buffer for S/R

   // Current indicator values
   double            m_fastRsi_curr;
   double            m_fastRsi_prev;
   double            m_dailyRsi;
   double            m_sar;

   // Trading variables
   double            m_priceSens;                  // Price sensitivity
   int               m_prevPosType;                // Previous position type
   bool              m_longModified;               // Long position modified flag
   bool              m_shortModified;              // Short position modified flag
   ulong             m_longTicket;                 // Long position ticket
   ulong             m_shortTicket;                // Short position ticket
   double            m_sl_long;                    // Long stop loss
   double            m_tp_long;                    // Long take profit
   double            m_sl_short;                   // Short stop loss
   double            m_tp_short;                   // Short take profit
   double            m_longOpenPrice;              // Long position opening price
   double            m_shortOpenPrice;             // Short position opening price
   datetime          m_longOpenTime;               // Long position opening time
   datetime          m_shortOpenTime;              // Short position opening time

   // Support/Resistance trailing flags
   bool              m_longSrTrailingActive;       // Long S/R trailing active flag
   bool              m_shortSrTrailingActive;      // Short S/R trailing active flag

   // Profit score trailing flags
   bool              m_longProfitScoreTrailingActive;   // Long profit score trailing active flag
   bool              m_shortProfitScoreTrailingActive;  // Short profit score trailing active flag

   // Heartbeat logging variables
   datetime          m_lastHeartbeatTime;          // Last heartbeat timestamp
   int               m_heartbeatInterval;          // Heartbeat interval in seconds (1 hour)

public:
                     CSampleExpert(void);
                    ~CSampleExpert(void);
   bool              Init(void);
   bool              Processing(void);

protected:
   bool              InitCheckParameters(const int digits_adjust);
   bool              InitIndicators(void);
   bool              LongOpened(void);
   bool              ShortOpened(void);
   bool              LongModified(void);
   bool              ShortModified(void);
   bool              LongClosed(void);
   bool              ShortClosed(void);
   double            CalculateLotSize(double stopLossDistance);
   double            FindSupportLevelFromTime(datetime positionTime);
   double            FindResistanceLevelFromTime(datetime positionTime);
   double            CalculateSRRatio(double entryPrice, bool isLong);
   double            CalculateSRPriceChange(void);
   double            CalculateProfitScore(bool isLong);
   void              AssignStopLossAndTakeProfitIfMissing(void);
   void              LogHeartbeat(void);
  };
//--- global expert
CSampleExpert ExtExpert;
//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CSampleExpert::CSampleExpert(void) : m_adjusted_point(0),
                                     m_handle_fastRsi(INVALID_HANDLE),
                                     m_fastRsi_curr(0),
                                     m_fastRsi_prev(0),
                                     m_handle_dailyRsi(INVALID_HANDLE),
                                     m_dailyRsi(0),
                                     m_handle_sar(INVALID_HANDLE),
                                     m_sar(0),
                                     m_priceSens(0),
                                     m_longModified(false),
                                     m_shortModified(false),
                                     m_longTicket(-1),
                                     m_shortTicket(-1),
                                     m_sl_long(-1),
                                     m_tp_long(-1),
                                     m_sl_short(-1),
                                     m_tp_short(-1),
                                     m_longOpenPrice(-1),
                                     m_shortOpenPrice(-1),
                                     m_longOpenTime(0),
                                     m_shortOpenTime(0),
                                     m_longSrTrailingActive(false),
                                     m_shortSrTrailingActive(false),
                                     m_longProfitScoreTrailingActive(false),
                                     m_shortProfitScoreTrailingActive(false),
                                     m_lastHeartbeatTime(0),
                                     m_heartbeatInterval(3600)
  {
   ArraySetAsSeries(m_buff_fastRSI,true);
   ArraySetAsSeries(m_buff_dailyRSI,true);
   ArraySetAsSeries(m_buff_SAR,true);
   ArraySetAsSeries(m_buff_high,true);
   ArraySetAsSeries(m_buff_low,true);
  }
//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CSampleExpert::~CSampleExpert(void)
  {
  }
//+------------------------------------------------------------------+
//| Initialization and checking for input parameters                 |
//+------------------------------------------------------------------+
bool CSampleExpert::Init(void)
  {
   // Initialize trading objects
   m_symbol.Name(Symbol());
   m_trade.SetExpertMagicNumber(RsiStochSwing);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(Symbol());
   m_priceSens = MathPow(10, -1 * m_symbol.Digits());

   // Set previous position type for alternating trades
   if(m_position.Select(Symbol()))
      m_prevPosType = m_position.PositionType();
   else
      m_prevPosType = (MathRand() > 16383.5) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;

   // Adjust point value for 3 or 5 digit brokers
   int digits_adjust = 1;
   if(m_symbol.Digits() == 3 || m_symbol.Digits() == 5)
      digits_adjust = 10;
   m_adjusted_point = m_symbol.Point() * digits_adjust;

   // Set trading deviation
   m_trade.SetDeviationInPoints(3 * digits_adjust);
//---
   if(!InitCheckParameters(digits_adjust))
      return(false);
   if(!InitIndicators())
      return(false);

   // Log initial heartbeat on EA startup
   printf("=== EA INITIALIZED === %s ===", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   printf("EA: RSI Swing SAR No Stop V12 started successfully");
   printf("Parameters: Risk=%.1f%%, Hard Stop=%.0f, S/R Band=[%.1f-%.1f], S/R Price Change=%.1f%%, S/R Trailing=%s, Profit Score Trailing=%s",
          InpRiskPercent, InpHardStopOffset, InpSRLowThr, InpSRHighThr, InpSrPriceChange, InpUseSrTrailing ? "ON" : "OFF", InpUseProfitScoreTrailing ? "ON" : "OFF");
   if(InpUseProfitScoreTrailing)
      printf("Profit Score Parameters: Threshold=%.3f, Profit Max=%.0f, Time Max=%.0f", InpProfitScoreThr, InpProfitNormMax, InpOpenTimeNormMax);
   printf("Heartbeat logging enabled - will report status every hour");

//--- succeed
   return(true);
  }
//+------------------------------------------------------------------+
//| Checking for input parameters                                    |
//+------------------------------------------------------------------+
bool CSampleExpert::InitCheckParameters(const int digits_adjust)
  {
   // Validate risk percentage (only when not using fixed lot size)
   if(!InpUseFixedLotSize && (InpRiskPercent <= 0 || InpRiskPercent > 100))
     {
      printf("Risk percentage must be between 0 and 100, current value: %f", InpRiskPercent);
      return(false);
     }

   // Validate fixed lot size (only when using fixed lot size)
   if(InpUseFixedLotSize && InpFixedLotSize <= 0)
     {
      printf("Fixed lot size must be greater than 0, current value: %f", InpFixedLotSize);
      return(false);
     }



   // Validate fixed lot size against symbol specifications
   if(InpUseFixedLotSize)
     {
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      if(InpFixedLotSize < minLot)
        {
         printf("Fixed lot size (%f) is below minimum allowed (%f)", InpFixedLotSize, minLot);
         return(false);
        }

      if(InpFixedLotSize > maxLot)
        {
         printf("Fixed lot size (%f) is above maximum allowed (%f)", InpFixedLotSize, maxLot);
         return(false);
        }

      // Validate lot step - this is just informational, we'll normalize in CalculateLotSize
      printf("Fixed lot size validation: Input=%f, Min=%f, Max=%f, Step=%f", InpFixedLotSize, minLot, maxLot, lotStep);
     }

   return(true);
  }
//+------------------------------------------------------------------+
//| Initialization of the indicators                                 |
//+------------------------------------------------------------------+
bool CSampleExpert::InitIndicators(void)
  {
   // Create fast RSI indicator
   if(m_handle_fastRsi == INVALID_HANDLE)
      if((m_handle_fastRsi = iRSI(NULL, InpTimeFrame, InpFastRSIPeriod, PRICE_MEDIAN)) == INVALID_HANDLE)
      {
         printf("Error creating fast RSI indicator");
         return(false);
      }

   // Create daily RSI indicator
   if(m_handle_dailyRsi == INVALID_HANDLE)
      if((m_handle_dailyRsi = iRSI(NULL, PERIOD_D1, InpDailyRSIPeriod, PRICE_MEDIAN)) == INVALID_HANDLE)
      {
         printf("Error creating daily RSI indicator");
         return(false);
      }

   // Create SAR indicator
   if(m_handle_sar == INVALID_HANDLE)
      if((m_handle_sar = iSAR(NULL, InpTimeFrame, InpSARStepSize, 0.2)) == INVALID_HANDLE)
      {
         printf("Error creating PSAR indicator");
         return(false);
      }

   return(true);
  }
bool CSampleExpert::LongClosed(void)
{
   bool res = false;
   double bid = m_symbol.Bid();
   m_position.SelectByTicket(m_longTicket);

   // Check exit conditions
   bool exitCond = (m_fastRsi_curr > 80 && m_position.Profit() > 0);
   bool timeProfitOut = (TimeCurrent() - m_position.Time() > InpTimeout && m_position.Profit() > 0);
   bool slOrTp = (m_sl_long != -1 && m_tp_long != -1) ? (bid >= m_tp_long || bid <= m_sl_long) : false;

//   if(exitCond || (timeProfitOut && InpTimeoutEnable) || slOrTp)
   if((timeProfitOut && InpTimeoutEnable) || slOrTp)
   {
      // Debug logging to show why position is being closed
      if(exitCond)
         printf("Closing long position: RSI exit condition (fastRSI=%.2f > 80 && profit=%.2f > 0)", m_fastRsi_curr, m_position.Profit());
      if(timeProfitOut && InpTimeoutEnable)
         printf("Closing long position: Timeout condition (time=%.0f > %d && profit=%.2f > 0)", (double)(TimeCurrent() - m_position.Time()), InpTimeout, m_position.Profit());
      if(slOrTp)
         printf("Closing long position: SL/TP hit (bid=%.2f, SL=%.2f, TP=%.2f)", bid, m_sl_long, m_tp_long);

      if(m_trade.PositionClose(Symbol()))
      {
         printf("Position by %s to be closed", Symbol());
         m_longTicket = -1; // Reset ticket after closing
         m_sl_long = -1;    // Reset SL/TP
         m_tp_long = -1;
         m_longOpenPrice = -1;
         m_longOpenTime = 0;
         m_longSrTrailingActive = false;
         m_longProfitScoreTrailingActive = false;
      }
      else
         printf("Error closing position by %s : '%s'", Symbol(), m_trade.ResultComment());
      res = true;
   }
   return(res);
}
bool CSampleExpert::ShortClosed(void)
{
   bool res = false;
   m_position.SelectByTicket(m_shortTicket);
   double ask = m_symbol.Ask();

   // Check exit conditions
   bool exitCond = (m_fastRsi_curr < 20 && m_position.Profit() > 0);
   bool timeProfitOut = (TimeCurrent() - m_position.Time() > InpTimeout && m_position.Profit() > 0);
   bool slOrTp = (m_sl_short != -1 && m_tp_short != -1) ? (ask <= m_tp_short || ask >= m_sl_short) : false;

//   if(exitCond || (timeProfitOut && InpTimeoutEnable) || slOrTp)
   if((timeProfitOut && InpTimeoutEnable) || slOrTp)
   {
      // Debug logging to show why position is being closed
      if(exitCond)
         printf("Closing short position: RSI exit condition (fastRSI=%.2f < 20 && profit=%.2f > 0)", m_fastRsi_curr, m_position.Profit());
      if(timeProfitOut && InpTimeoutEnable)
         printf("Closing short position: Timeout condition (time=%.0f > %d && profit=%.2f > 0)", (double)(TimeCurrent() - m_position.Time()), InpTimeout, m_position.Profit());
      if(slOrTp)
         printf("Closing short position: SL/TP hit (ask=%.2f, SL=%.2f, TP=%.2f)", ask, m_sl_short, m_tp_short);

      if(m_trade.PositionClose(Symbol()))
      {
         printf("Position by %s to be closed", Symbol());
         m_shortTicket = -1; // Reset ticket after closing
         m_sl_short = -1;    // Reset SL/TP
         m_tp_short = -1;
         m_shortOpenPrice = -1;
         m_shortOpenTime = 0;
         m_shortSrTrailingActive = false;
         m_shortProfitScoreTrailingActive = false;
      }
      else
         printf("Error closing position by %s : '%s'", Symbol(), m_trade.ResultComment());
      res = true;
   }
   return(res);
}
//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage or fixed lot size    |
//+------------------------------------------------------------------+
double CSampleExpert::CalculateLotSize(double stopLossDistance)
{
   double lotSize = 0;

   // Use fixed lot size if enabled
   if(InpUseFixedLotSize)
   {
      // Get symbol specifications for normalization
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      // Normalize the fixed lot size to valid step
      lotSize = MathRound(InpFixedLotSize / lotStep) * lotStep;

      // Ensure lot size is within limits
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));

      // Log if normalization occurred
      if(MathAbs(lotSize - InpFixedLotSize) > 0.0001)
      {
         printf("Fixed lot size normalized from %f to %f (step: %f)", InpFixedLotSize, lotSize, lotStep);
      }
   }
   else
   {
      // Calculate lot size based on risk percentage
      // Get account balance
      double accountBalance = m_account.Balance();

      // Calculate risk amount in account currency
      double riskAmount = accountBalance * (InpRiskPercent / 100.0);

      // Get symbol specifications
      double tickValue = m_symbol.TickValue();
      double tickSize = m_symbol.TickSize();

      if(tickSize > 0 && tickValue > 0)
      {
         // Calculate value per point
         double valuePerPoint = (tickValue / tickSize);

         // Calculate lot size based on risk
         if(stopLossDistance > 0)
            lotSize = riskAmount / (stopLossDistance * valuePerPoint);
      }

      // Normalize lot size to symbol specifications
      double minLot = m_symbol.LotsMin();
      double maxLot = m_symbol.LotsMax();
      double lotStep = m_symbol.LotsStep();

      // Round to lot step
      lotSize = MathRound(lotSize / lotStep) * lotStep;

      // Ensure lot size is within limits
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   }

   return lotSize;
}

//+------------------------------------------------------------------+
//| Find support level from specific time (for position opening)    |
//+------------------------------------------------------------------+
double CSampleExpert::FindSupportLevelFromTime(datetime positionTime)
{
   double supportLevel = 0;
   double lowestLow = DBL_MAX;



   // Calculate the bar index for the position opening time using S/R timeframe
   int positionBarIndex = iBarShift(NULL, InpSRTimeFrame, positionTime);
   int startIndex;
   if(positionBarIndex < 0)
   {
      // Position time not found on S/R timeframe, find the nearest bar
      // This can happen when position time doesn't align with S/R timeframe bars
      datetime nearestBarTime = 0;
      int barsCount = iBars(NULL, InpSRTimeFrame);

      // Find the bar that contains or is closest to the position time
      for(int i = 1; i < MathMin(barsCount, 100); i++) // Check up to 100 bars back
      {
         datetime barTime = iTime(NULL, InpSRTimeFrame, i);
         if(barTime <= positionTime)
         {
            positionBarIndex = i;
            nearestBarTime = barTime;
            break;
         }
      }

      if(positionBarIndex < 0)
      {
         printf("Warning: Could not find suitable bar for position time %s, using index 1",
                TimeToString(positionTime, TIME_DATE|TIME_MINUTES));
         startIndex = 1;
      }
      else
      {
         startIndex = positionBarIndex + 1;
         printf("Found nearest bar at index %d (time: %s) for position time %s",
                positionBarIndex, TimeToString(nearestBarTime, TIME_DATE|TIME_MINUTES),
                TimeToString(positionTime, TIME_DATE|TIME_MINUTES));
      }
   }
   else
   {
      // Get low prices starting from position opening time, looking back
      startIndex = positionBarIndex + 1; // Start from the bar after position opening
   }
   if(CopyLow(NULL, InpSRTimeFrame, startIndex, InpSRLookbackPeriod, m_buff_low) == InpSRLookbackPeriod)
   {
      // Find the lowest low in the lookback period
      for(int i = 0; i < InpSRLookbackPeriod; i++)
      {
         if(m_buff_low[i] < lowestLow)
            lowestLow = m_buff_low[i];
      }
      supportLevel = lowestLow;
   }
   else
   {
      printf("Error copying low price data for support calculation from position time");
      // Fallback to hard stop offset
      supportLevel = m_position.PriceOpen() - InpHardStopOffset;
   }

   return supportLevel;
}
//+------------------------------------------------------------------+
//| Find resistance level from specific time (for position opening) |
//+------------------------------------------------------------------+
double CSampleExpert::FindResistanceLevelFromTime(datetime positionTime)
{
   double resistanceLevel = 0;
   double highestHigh = 0;



   // Calculate the bar index for the position opening time using S/R timeframe
   int positionBarIndex = iBarShift(NULL, InpSRTimeFrame, positionTime);
   int startIndex;
   if(positionBarIndex < 0)
   {
      // Position time not found on S/R timeframe, find the nearest bar
      // This can happen when position time doesn't align with S/R timeframe bars
      datetime nearestBarTime = 0;
      int barsCount = iBars(NULL, InpSRTimeFrame);

      // Find the bar that contains or is closest to the position time
      for(int i = 1; i < MathMin(barsCount, 100); i++) // Check up to 100 bars back
      {
         datetime barTime = iTime(NULL, InpSRTimeFrame, i);
         if(barTime <= positionTime)
         {
            positionBarIndex = i;
            nearestBarTime = barTime;
            break;
         }
      }

      if(positionBarIndex < 0)
      {
         printf("Warning: Could not find suitable bar for position time %s, using index 1",
                TimeToString(positionTime, TIME_DATE|TIME_MINUTES));
         startIndex = 1;
      }
      else
      {
         startIndex = positionBarIndex + 1;
         printf("Found nearest bar at index %d (time: %s) for position time %s",
                positionBarIndex, TimeToString(nearestBarTime, TIME_DATE|TIME_MINUTES),
                TimeToString(positionTime, TIME_DATE|TIME_MINUTES));
      }
   }
   else
   {
      // Get high prices starting from position opening time, looking back
      startIndex = positionBarIndex + 1; // Start from the bar after position opening
   }
   if(CopyHigh(NULL, InpSRTimeFrame, startIndex, InpSRLookbackPeriod, m_buff_high) == InpSRLookbackPeriod)
   {
      // Find the highest high in the lookback period
      for(int i = 0; i < InpSRLookbackPeriod; i++)
      {
         if(m_buff_high[i] > highestHigh)
            highestHigh = m_buff_high[i];
      }
      resistanceLevel = highestHigh;
   }
   else
   {
      printf("Error copying high price data for resistance calculation from position time");
      // Fallback to hard stop offset
      resistanceLevel = m_position.PriceOpen() + InpHardStopOffset;
   }

   return resistanceLevel;
}
//+------------------------------------------------------------------+
//| Calculate Support/Resistance ratio for entry filter             |
//+------------------------------------------------------------------+
double CSampleExpert::CalculateSRRatio(double entryPrice, bool isLong)
{
   double supportLevel = FindSupportLevelFromTime(TimeCurrent());
   double resistanceLevel = FindResistanceLevelFromTime(TimeCurrent());

   double supportDistance = entryPrice - supportLevel;
   double resistanceDistance = resistanceLevel - entryPrice;

   // Ensure distances are positive
   if(supportDistance <= 0 || resistanceDistance <= 0)
   {
      printf("Invalid S/R distances: support=%.2f, resistance=%.2f, entry=%.2f",
             supportDistance, resistanceDistance, entryPrice);
      return -1; // Invalid ratio
   }

   double ratio;
   if(isLong)
   {
      // For long: resistance distance / support distance
      ratio = resistanceDistance / supportDistance;
   }
   else
   {
      // For short: support distance / resistance distance
      ratio = supportDistance / resistanceDistance;
   }

   return ratio;
}
//+------------------------------------------------------------------+
//| Calculate Support/Resistance price change percentage            |
//+------------------------------------------------------------------+
double CSampleExpert::CalculateSRPriceChange(void)
{
   double supportLevel = FindSupportLevelFromTime(TimeCurrent());
   double resistanceLevel = FindResistanceLevelFromTime(TimeCurrent());

   // Ensure support level is valid and positive
   if(supportLevel <= 0)
   {
      printf("Invalid support level: %.2f", supportLevel);
      return -1; // Invalid price change
   }

   // Calculate price change percentage: ((resistance - support)/support)*100
   double priceChangePercent = ((resistanceLevel - supportLevel) / supportLevel) * 100.0;

   return priceChangePercent;
}
//+------------------------------------------------------------------+
//| Calculate profit score for trailing activation                  |
//+------------------------------------------------------------------+
double CSampleExpert::CalculateProfitScore(bool isLong)
{
   double profitScore = 0.0;

   if(isLong && m_longTicket > 0)
   {
      if(m_position.SelectByTicket(m_longTicket))
      {
         // Calculate profit in points: (current price - open price) * Point()
         double currentPrice = m_symbol.Bid();
         double openPrice = (m_longOpenPrice > 0) ? m_longOpenPrice : m_position.PriceOpen();
         double profitPoints = (currentPrice - openPrice) * m_symbol.Point();

         // Calculate position open time in seconds
         datetime openTime = (m_longOpenTime > 0) ? m_longOpenTime : m_position.Time();
         double openTimeSeconds = (double)(TimeCurrent() - openTime);

         // Calculate normalized values using lognorm(x) = log(x+1)/log(xmax+1)
         double profitNorm = MathLog(profitPoints + 1) / MathLog(InpProfitNormMax + 1);
         double timeNorm = MathLog(openTimeSeconds + 1) / MathLog(InpOpenTimeNormMax + 1);

         // Calculate profit score
         profitScore = profitNorm * timeNorm;
      }
   }
   else if(!isLong && m_shortTicket > 0)
   {
      if(m_position.SelectByTicket(m_shortTicket))
      {
         // Calculate profit in points: (open price - current price) * Point()
         double currentPrice = m_symbol.Ask();
         double openPrice = (m_shortOpenPrice > 0) ? m_shortOpenPrice : m_position.PriceOpen();
         double profitPoints = (openPrice - currentPrice) * m_symbol.Point();

         // Calculate position open time in seconds
         datetime openTime = (m_shortOpenTime > 0) ? m_shortOpenTime : m_position.Time();
         double openTimeSeconds = (double)(TimeCurrent() - openTime);

         // Calculate normalized values using lognorm(x) = log(x+1)/log(xmax+1)
         double profitNorm = MathLog(profitPoints + 1) / MathLog(InpProfitNormMax + 1);
         double timeNorm = MathLog(openTimeSeconds + 1) / MathLog(InpOpenTimeNormMax + 1);

         // Calculate profit score
         profitScore = profitNorm * timeNorm;
      }
   }

   return profitScore;
}
//+------------------------------------------------------------------+
//| Assign stop loss and take profit if position doesn't have them  |
//+------------------------------------------------------------------+
void CSampleExpert::AssignStopLossAndTakeProfitIfMissing(void)
{
   // Check long position only if we have a valid ticket and missing SL/TP
   if(m_longTicket > 0 && (m_sl_long == -1 || m_tp_long == -1))
   {
      if(m_position.SelectByTicket(m_longTicket) && m_position.PositionType() == POSITION_TYPE_BUY)
      {
         // Assign stop loss if missing
         if(m_sl_long == -1)
         {
            datetime posTime = m_position.Time();
            if(posTime <= 0 && m_longOpenTime > 0)
            {
               posTime = m_longOpenTime; // Use stored opening time as fallback
            }

            if(posTime > 0)
            {
               double supportLevel = FindSupportLevelFromTime(posTime);
               m_sl_long = supportLevel - 10;
               printf("Assigned support-based stop loss for long position: %.2f", m_sl_long);
            }
            else
            {
               printf("Warning: No valid position time for long position, using fallback SL");
               m_sl_long = m_position.PriceOpen() - InpHardStopOffset;
            }
         }

         // Assign take profit if missing
         if(m_tp_long == -1)
         {
            double slDistance = m_position.PriceOpen() - m_sl_long;
            double tpOffset = InpRRfactor * slDistance;
            m_tp_long = m_position.PriceOpen() + tpOffset + m_symbol.Spread() * m_priceSens;
            printf("Assigned take profit for long position: %.2f", m_tp_long);
         }
      }
   }

   // Check short position only if we have a valid ticket and missing SL/TP
   if(m_shortTicket > 0 && (m_sl_short == -1 || m_tp_short == -1))
   {
      if(m_position.SelectByTicket(m_shortTicket) && m_position.PositionType() == POSITION_TYPE_SELL)
      {
         // Assign stop loss if missing
         if(m_sl_short == -1)
         {
            datetime posTime = m_position.Time();
            if(posTime <= 0 && m_shortOpenTime > 0)
            {
               posTime = m_shortOpenTime; // Use stored opening time as fallback
            }

            if(posTime > 0)
            {
               double resistanceLevel = FindResistanceLevelFromTime(posTime);
               m_sl_short = resistanceLevel + 10;
               printf("Assigned resistance-based stop loss for short position: %.2f", m_sl_short);
            }
            else
            {
               printf("Warning: No valid position time for short position, using fallback SL");
               m_sl_short = m_position.PriceOpen() + InpHardStopOffset;
            }
         }

         // Assign take profit if missing
         if(m_tp_short == -1)
         {
            double slDistance = m_sl_short - m_position.PriceOpen();
            double tpOffset = InpRRfactor * slDistance;
            m_tp_short = m_position.PriceOpen() - tpOffset - m_symbol.Spread() * m_priceSens;
            // Ensure TP is not negative
            m_tp_short = (m_tp_short < 0) ? SYMBOL_TRADE_STOPS_LEVEL * m_priceSens : m_tp_short;
            printf("Assigned take profit for short position: %.2f", m_tp_short);
         }
      }
   }
}
//+------------------------------------------------------------------+
//| Log heartbeat with EA status and position information           |
//+------------------------------------------------------------------+
void CSampleExpert::LogHeartbeat(void)
{
   // Update heartbeat timestamp
   m_lastHeartbeatTime = TimeCurrent();

   // Get account information
   double balance = m_account.Balance();
   double equity = m_account.Equity();
   double freeMargin = m_account.FreeMargin();

   // Count positions for this symbol
   int posCount = 0;
   string posInfo = "";

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         m_position.SelectByIndex(i);
         posCount++;

         string posType = (m_position.PositionType() == POSITION_TYPE_BUY) ? "LONG" : "SHORT";
         double openPrice = m_position.PriceOpen();
         double lotSize = m_position.Volume();
         double profit = m_position.Profit();
         datetime openTime = m_position.Time();

         // Calculate support and resistance levels for the position
         datetime posTime = openTime;
         if(posTime <= 0)
         {
            // Use stored opening time as fallback
            posTime = (m_position.PositionType() == POSITION_TYPE_BUY) ? m_longOpenTime : m_shortOpenTime;
         }

         double supportLevel = (posTime > 0) ? FindSupportLevelFromTime(posTime) : 0;
         double resistanceLevel = (posTime > 0) ? FindResistanceLevelFromTime(posTime) : 0;

         // Calculate position duration
         int durationHours = (int)((TimeCurrent() - openTime) / 3600);
         int durationMins = (int)(((TimeCurrent() - openTime) % 3600) / 60);

         posInfo += StringFormat("%s: %.2f lots @ %.5f, P&L: %.2f, Support: %.5f, Resistance: %.5f, Duration: %dh%dm | ",
                                posType, lotSize, openPrice, profit, supportLevel, resistanceLevel, durationHours, durationMins);
      }
   }

   // Log heartbeat information
   printf("=== HEARTBEAT === %s ===", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   printf("Account: Balance=%.2f, Equity=%.2f, Free Margin=%.2f", balance, equity, freeMargin);
   printf("Positions: %d active | %s", posCount, (posCount > 0) ? posInfo : "No positions");
   printf("Indicators: Fast RSI=%.1f, Daily RSI=%.1f, SAR=%.5f",
          m_fastRsi_curr, m_dailyRsi, m_sar);
   printf("Market: Bid=%.5f, Ask=%.5f, Spread=%.1f points",
          m_symbol.Bid(), m_symbol.Ask(), m_symbol.Spread());
   printf("EA Status: Risk=%.1f%%, Hard Stop=%.0f, S/R Band=[%.1f-%.1f], S/R Price Change=%.1f%%, S/R Trailing=%s, Profit Score Trailing=%s",
          InpRiskPercent, InpHardStopOffset, InpSRLowThr, InpSRHighThr, InpSrPriceChange,
          InpUseSrTrailing ? "ON" : "OFF", InpUseProfitScoreTrailing ? "ON" : "OFF");
   printf("=== END HEARTBEAT ===");
}
//+------------------------------------------------------------------+
//| Check for long position modifying                                |
//+------------------------------------------------------------------+
bool CSampleExpert::LongModified(void)
{
   if(!m_position.SelectByTicket(m_longTicket))
   {
      printf("Warning: Could not select long position with ticket %I64u in LongModified", m_longTicket);
      return false;
   }

   bool res = false;

   // Assign support/resistance-based stop loss and take profit if missing
   AssignStopLossAndTakeProfitIfMissing();

   // Re-select the long position after AssignStopLossAndTakeProfitIfMissing()
   // which might have changed the position selection
   if(!m_position.SelectByTicket(m_longTicket))
   {
      printf("Error: Could not re-select long position ticket %I64u in LongModified", m_longTicket);
      return false;
   }

   double slOffset = InpHardStopOffset;
   slOffset = MathMax(slOffset, SYMBOL_TRADE_STOPS_LEVEL * m_priceSens);

   // Use stored opening price if available, otherwise try to get from position
   double positionOpenPrice = (m_longOpenPrice > 0) ? m_longOpenPrice : m_position.PriceOpen();
   if(positionOpenPrice <= 0)
      positionOpenPrice = m_symbol.Bid();

   // Calculate actual stop loss distance for trailing calculation
   double actualSLDistance = (m_sl_long != -1) ? (positionOpenPrice - m_sl_long) : slOffset;
   double ask = m_symbol.Ask();
   double bid = m_symbol.Bid();
   double sl = (m_sl_long == -1) ? (positionOpenPrice - slOffset) : m_sl_long;
   bool modify = false;

   // Note: TP is set once when position is opened, no need to recalculate here

   // Support/Resistance based take profit logic
   if(InpUseSrTrailing && !m_longSrTrailingActive)
   {
      // Check if price has reached resistance level
      datetime posTime = m_position.Time();
      if(posTime <= 0 && m_longOpenTime > 0)
      {
         // Fallback to stored opening time if position time is invalid
         posTime = m_longOpenTime;
         printf("Using stored opening time as fallback for long position S/R calculation");
      }

      if(posTime > 0) // Ensure valid position time
      {
         double resistanceLevel = FindResistanceLevelFromTime(posTime);
         if(bid >= resistanceLevel - InpSRBuffer*m_symbol.Point()) //Small buffer below resistance
         {
            m_longSrTrailingActive = true;
            printf("Long position: Price reached resistance level %.5f, activating SAR trailing", resistanceLevel);
         }
      }
      else
      {
         printf("Warning: No valid position time available for long position (ticket: %I64u), skipping S/R trailing check", m_longTicket);
      }
   }

   // Profit score based trailing logic
   if(InpUseProfitScoreTrailing && !m_longProfitScoreTrailingActive)
   {
      double profitScore = CalculateProfitScore(true);
      if(profitScore > InpProfitScoreThr)
      {
         m_longProfitScoreTrailingActive = true;
         printf("Long position: Profit score (%.4f) exceeded threshold (%.4f), activating SAR trailing", profitScore, InpProfitScoreThr);
      }
   }

   // Trail stop loss when in profit
   double profitPoints = bid - positionOpenPrice;
   double trailingThreshold = actualSLDistance * InpTrailingStopFactor;

   if(profitPoints > trailingThreshold)
   {
      // Determine if SAR trailing should be activated based on selected trailing methods
      bool activateSarTrailing = false;

      if(InpUseSrTrailing && InpUseProfitScoreTrailing)
      {
         // Both methods enabled: require both conditions to be met
         activateSarTrailing = m_longSrTrailingActive && m_longProfitScoreTrailingActive;
      }
      else if(InpUseSrTrailing)
      {
         // Only S/R trailing enabled
         activateSarTrailing = m_longSrTrailingActive;
      }
      else if(InpUseProfitScoreTrailing)
      {
         // Only profit score trailing enabled
         activateSarTrailing = m_longProfitScoreTrailingActive;
      }
      else
      {
         // Neither method enabled: use original logic (profit threshold only)
         activateSarTrailing = true;
      }

      if(activateSarTrailing)
      {
         if(bid > m_sar)
         {
            sl = m_sar;
            m_sl_long = sl;
            modify = true;
            printf("Long position: SAR trailing activated, SL moved to %.2f", sl);
         }
      }
   }
   // Note: Do not modify support/resistance-based stop loss when price moves against position
   // The SL should remain at the support level as originally calculated

   if(modify)
   {
      res = true;
   }

   return(res);
}
//+------------------------------------------------------------------+
//| Check for short position modifying                               |
//+------------------------------------------------------------------+
bool CSampleExpert::ShortModified(void)
{
   if(!m_position.SelectByTicket(m_shortTicket))
   {
      printf("Warning: Could not select short position with ticket %I64u in ShortModified", m_shortTicket);
      return false;
   }

   bool res = false;

   // Assign support/resistance-based stop loss and take profit if missing
   AssignStopLossAndTakeProfitIfMissing();

   // Re-select the short position after AssignStopLossAndTakeProfitIfMissing()
   // which might have changed the position selection
   if(!m_position.SelectByTicket(m_shortTicket))
   {
      printf("Error: Could not re-select short position ticket %I64u in ShortModified", m_shortTicket);
      return false;
   }

   double slOffset = InpHardStopOffset;
   slOffset = MathMax(slOffset, SYMBOL_TRADE_STOPS_LEVEL * m_priceSens);

   // Use stored opening price if available, otherwise try to get from position
   double positionOpenPrice = (m_shortOpenPrice > 0) ? m_shortOpenPrice : m_position.PriceOpen();
   if(positionOpenPrice <= 0)
      positionOpenPrice = m_symbol.Ask();

   // Calculate actual stop loss distance for trailing calculation
   double actualSLDistance = (m_sl_short != -1) ? (m_sl_short - positionOpenPrice) : slOffset;
   double ask = m_symbol.Ask();
   double bid = m_symbol.Bid();
   double sl = (m_sl_short == -1) ? (positionOpenPrice + slOffset) : m_sl_short;
   bool modify = false;

   // Note: TP is set once when position is opened, no need to recalculate here

   // Support/Resistance based take profit logic
   if(InpUseSrTrailing && !m_shortSrTrailingActive)
   {
      // Check if price has reached support level
      datetime posTime = m_position.Time();
      if(posTime <= 0 && m_shortOpenTime > 0)
      {
         // Fallback to stored opening time if position time is invalid
         posTime = m_shortOpenTime;
         printf("Using stored opening time as fallback for short position S/R calculation");
      }

      if(posTime > 0) // Ensure valid position time
      {
         double supportLevel = FindSupportLevelFromTime(posTime);
         if(ask <= supportLevel + InpSRBuffer*m_symbol.Point()) //Small buffer above support
         {
            m_shortSrTrailingActive = true;
            printf("Short position: Price reached support level %.5f, activating SAR trailing", supportLevel);
         }
      }
      else
      {
         printf("Warning: No valid position time available for short position (ticket: %I64u), skipping S/R trailing check", m_shortTicket);
      }
   }

   // Profit score based trailing logic
   if(InpUseProfitScoreTrailing && !m_shortProfitScoreTrailingActive)
   {
      double profitScore = CalculateProfitScore(false);
      if(profitScore > InpProfitScoreThr)
      {
         m_shortProfitScoreTrailingActive = true;
         printf("Short position: Profit score (%.4f) exceeded threshold (%.4f), activating SAR trailing", profitScore, InpProfitScoreThr);
      }
   }

   // Trail stop loss when in profit
   double profitPoints = positionOpenPrice - ask;
   double trailingThreshold = actualSLDistance * InpTrailingStopFactor;

   if(profitPoints > trailingThreshold)
   {
      // Determine if SAR trailing should be activated based on selected trailing methods
      bool activateSarTrailing = false;

      if(InpUseSrTrailing && InpUseProfitScoreTrailing)
      {
         // Both methods enabled: require both conditions to be met
         activateSarTrailing = m_shortSrTrailingActive && m_shortProfitScoreTrailingActive;
      }
      else if(InpUseSrTrailing)
      {
         // Only S/R trailing enabled
         activateSarTrailing = m_shortSrTrailingActive;
      }
      else if(InpUseProfitScoreTrailing)
      {
         // Only profit score trailing enabled
         activateSarTrailing = m_shortProfitScoreTrailingActive;
      }
      else
      {
         // Neither method enabled: use original logic (profit threshold only)
         activateSarTrailing = true;
      }

      if(activateSarTrailing)
      {
         if(ask < m_sar)
         {
            sl = m_sar;
            m_sl_short = sl;
            modify = true;
            printf("Short position: SAR trailing activated, SL moved to %.2f", sl);
         }
      }
   }
   // Note: Do not modify resistance-based stop loss when price moves against position
   // The SL should remain at the resistance level as originally calculated

   if(modify)
   {
      res = true;
   }

   return(res);
}
//+------------------------------------------------------------------+
//| Open position
//+------------------------------------------------------------------+
bool CSampleExpert::LongOpened(void)
{
   bool res = false;

   // Check long entry conditions
   if(m_fastRsi_prev < 30 && m_fastRsi_curr > 30 && !NoTradeTime() &&
      (m_dailyRsi >= InpDailyRsiUpperLimit || m_dailyRsi <= 100 - InpDailyRsiUpperLimit))
   {
      double ask = m_symbol.Ask();
      double bid = m_symbol.Bid();
      double slOffset = InpHardStopOffset;
      slOffset = MathMax(slOffset, SYMBOL_TRADE_STOPS_LEVEL * m_priceSens);

      // Check S/R ratio filter
      double srRatio = CalculateSRRatio(ask, true);
      if(srRatio < 0 || srRatio < InpSRLowThr || srRatio > InpSRHighThr)
      {
         printf("Skipping long trade: S/R ratio (%.2f) outside band [%.2f, %.2f]", srRatio, InpSRLowThr, InpSRHighThr);
         return false;
      }

      // Check S/R price change filter
      double srPriceChange = CalculateSRPriceChange();
      if(srPriceChange < 0 || srPriceChange < InpSrPriceChange)
      {
         printf("Skipping long trade: S/R price change (%.2f%%) below threshold (%.2f%%)", srPriceChange, InpSrPriceChange);
         return false;
      }

      // Calculate support-based stop loss
      double supportLevel = FindSupportLevelFromTime(TimeCurrent());
      double supportBasedSL = supportLevel - InpSRBuffer*m_symbol.Point(); // Small buffer below support
      double supportDistance = bid - supportBasedSL;

      // Validate support-based stop loss distance
      if(supportDistance > slOffset)
      {
         printf("Skipping long trade: Support-based SL distance (%f) exceeds hard stop offset (%f)", supportDistance, slOffset);
         return false;
      }

      double tpOffset = InpRRfactor * supportDistance;
      double sl = supportBasedSL;
      double tp = ask + tpOffset + m_symbol.Spread() * m_priceSens;
      sl = (sl < 0) ? SYMBOL_TRADE_STOPS_LEVEL * m_priceSens : sl;

      m_sl_long = sl;
      m_tp_long = tp;
      m_longOpenPrice = ask; // Store the actual opening price
      m_longOpenTime = TimeCurrent(); // Store the opening time
      m_longSrTrailingActive = false; // Reset S/R trailing flag
      m_longProfitScoreTrailingActive = false; // Reset profit score trailing flag

      // Calculate lot size using support-based distance
      double lotSize = CalculateLotSize(supportDistance);

      Print(Symbol(), " Long position opened - SL: ", m_sl_long, " TP: ", m_tp_long, " Lot: ", lotSize);

      if(m_trade.Buy(lotSize, Symbol(), ask))
      {
         m_longTicket = m_trade.ResultOrder(); // Capture the ticket immediately
         printf("Position by %s to be opened, ticket: %I64u", Symbol(), m_longTicket);
         m_prevPosType = ORDER_TYPE_BUY;
      }
      else
      {
         printf("Error opening BUY position by %s : '%s'", Symbol(), m_trade.ResultComment());
         printf("Open parameters : price=%f,SL=%f,TP=%f,LotSize=%f", ask, sl, tp, lotSize);
      }
      res = true;
   }

   return(res);
}
bool CSampleExpert::ShortOpened(void)
{
   bool res = false;

   // Check short entry conditions
   if(m_fastRsi_prev > 70 && m_fastRsi_curr < 70 && !NoTradeTime() &&
      (m_dailyRsi >= InpDailyRsiUpperLimit || m_dailyRsi <= 100 - InpDailyRsiUpperLimit))
   {
      double ask = m_symbol.Ask();
      double bid = m_symbol.Bid();
      double slOffset = InpHardStopOffset;
      slOffset = MathMax(slOffset, SYMBOL_TRADE_STOPS_LEVEL * m_priceSens);

      // Check S/R ratio filter
      double srRatio = CalculateSRRatio(bid, false);
      if(srRatio < 0 || srRatio < InpSRLowThr || srRatio > InpSRHighThr)
      {
         printf("Skipping short trade: S/R ratio (%.2f) outside band [%.2f, %.2f]", srRatio, InpSRLowThr, InpSRHighThr);
         return false;
      }

      // Check S/R price change filter
      double srPriceChange = CalculateSRPriceChange();
      if(srPriceChange < 0 || srPriceChange < InpSrPriceChange)
      {
         printf("Skipping short trade: S/R price change (%.2f%%) below threshold (%.2f%%)", srPriceChange, InpSrPriceChange);
         return false;
      }

      // Calculate resistance-based stop loss
      double resistanceLevel = FindResistanceLevelFromTime(TimeCurrent());
      double resistanceBasedSL = resistanceLevel + InpSRBuffer*m_symbol.Point(); // Small buffer above resistance
      double resistanceDistance = resistanceBasedSL - ask;


      // Validate resistance-based stop loss distance
      if(resistanceDistance > slOffset)
      {
         printf("Skipping short trade: Resistance-based SL distance (%f) exceeds hard stop offset (%f)", resistanceDistance, slOffset);
         return false;
      }

      double tpOffset = InpRRfactor * resistanceDistance;
      double sl = resistanceBasedSL;
      double tp = bid - tpOffset - m_symbol.Spread() * m_priceSens;
      tp = (tp < 0) ? SYMBOL_TRADE_STOPS_LEVEL * m_priceSens : tp;

      m_tp_short = tp;
      m_sl_short = sl;
      m_shortOpenPrice = bid; // Store the actual opening price
      m_shortOpenTime = TimeCurrent(); // Store the opening time
      m_shortSrTrailingActive = false; // Reset S/R trailing flag
      m_shortProfitScoreTrailingActive = false; // Reset profit score trailing flag

      // Calculate lot size using resistance-based distance
      double lotSize = CalculateLotSize(resistanceDistance);

      Print(Symbol(), " Short position opened - SL: ", m_sl_short, " TP: ", m_tp_short, " Lot: ", lotSize);

      if(m_trade.Sell(lotSize, Symbol(), bid))
      {
         m_shortTicket = m_trade.ResultOrder(); // Capture the ticket immediately
         printf("Position by %s to be opened, ticket: %I64u", Symbol(), m_shortTicket);
         m_prevPosType = ORDER_TYPE_SELL;
      }
      else
      {
         printf("Error opening SELL position by %s : '%s'", Symbol(), m_trade.ResultComment());
         printf("Open parameters : price=%f,SL=%f,TP=%f,LotSize=%f", bid, sl, tp, lotSize);
      }
      res = true;
   }

   return(res);
}

//+------------------------------------------------------------------+
//| main function returns true if any position processed             |
//+------------------------------------------------------------------+
bool CSampleExpert::Processing(void)
  {
   // Refresh market data
   if(!m_symbol.RefreshRates())
      return(false);

   // Check indicator calculations
   if(CopyBuffer(m_handle_fastRsi, 0, 0, 2, m_buff_fastRSI) != 2)
      return(false);
   if(CopyBuffer(m_handle_dailyRsi, 0, 0, 1, m_buff_dailyRSI) != 1)
      return(false);
   if(CopyBuffer(m_handle_sar, 0, 0, 1, m_buff_SAR) != 1)
      return(false);

   // Update indicator values
   m_fastRsi_curr = m_buff_fastRSI[0];
   m_fastRsi_prev = m_buff_fastRSI[1];
   m_dailyRsi = m_buff_dailyRSI[0];
   m_sar = m_buff_SAR[0];

   // Check for heartbeat logging (once per hour)
   datetime currentTime = TimeCurrent();
   if(m_lastHeartbeatTime == 0 || (currentTime - m_lastHeartbeatTime) >= m_heartbeatInterval)
   {
      LogHeartbeat();
   }

   // Count and identify positions for this symbol
   uint posNumber = 0;
   int posTotal = PositionsTotal();
   for(int i = 0; i < posTotal; i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         posNumber++;
         m_position.SelectByIndex(i);
         if(m_position.PositionType() == POSITION_TYPE_BUY)
            m_longTicket = m_position.Ticket();
         else if(m_position.PositionType() == POSITION_TYPE_SELL)
            m_shortTicket = m_position.Ticket();
      }
   }
   // Process positions based on count
   if(posNumber > 1)
   {
      Alert("Error on RsiSwing, posNumber > 1");
      return(false);
   }
   else if(posNumber == 1)
   {
      // Handle existing long position
      if(m_position.SelectByTicket(m_longTicket) && m_position.PositionType() == POSITION_TYPE_BUY)
      {
         if(LongClosed())
            return(true);
         m_longModified = LongModified();
      }

      // Handle existing short position
      if(m_position.SelectByTicket(m_shortTicket) && m_position.PositionType() == POSITION_TYPE_SELL)
      {
         if(ShortClosed())
            return(true);
         m_shortModified = ShortModified();
      }

      if(m_longModified || m_shortModified)
         return(true);
   }
   else if(posNumber == 0)
   {
      // Try to open new positions
      if(LongOpened())
         return(true);
      if(ShortOpened())
         return(true);
   }

   return(false);
  }
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit(void)
  {
//--- create all necessary objects
   if(!ExtExpert.Init())
      return(INIT_FAILED);
//--- succeed
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Check for new minute                                            |
//+------------------------------------------------------------------+
bool IsNewMin(int currMin)
{
   static int lastMin;
   if(currMin != lastMin)
   {
      lastMin = currMin;
      return true;
   }
   return false;
}
//+------------------------------------------------------------------+
//| Time filter function                                            |
//+------------------------------------------------------------------+
bool NoTradeTime()
{
   if(!InpTimeFilterEnable)
      return false;

   MqlDateTime cur_time;
   TimeCurrent(cur_time);
   bool started = false;
   bool ended = false;

   if((cur_time.hour == start_hour && cur_time.min >= start_min) || cur_time.hour > start_hour)
      started = true;

   if((cur_time.hour == end_hour && cur_time.min > end_min) || cur_time.hour > end_hour)
      ended = true;

   return (started && !ended);
}
//+------------------------------------------------------------------+
//| Expert tick handler                                             |
//+------------------------------------------------------------------+
void OnTick(void)
{
   MqlDateTime cur_time;
   TimeCurrent(cur_time);
   if(IsNewMin(cur_time.min))
   {
      ExtExpert.Processing();
   }
}
